export interface KnowledgeEntry {
  kb_id: number;
  project_id: number;
  title: string;
  content: string;
  solution: string | null;  // Changed to match backend's Optional[str]
  solution_id: number | null;  // Changed to match backend's Optional[int]
  uploaded_by: number;
  file_url: string | null;  // Changed to match backend's Optional[str]
  created_at: string;  // Backend sends datetime, frontend receives as string
  author_name: string | null;  // Changed to match backend's Optional[str]
  verified: boolean;  // Default is false in backend
  views: number;  // Default is 0 in backend
  likes: number;  // Default is 0 in backend
  system: string | null;  // Changed to match backend's Optional[str]
  type: string | null;  // Changed to match backend's Optional[str]
  is_favorite: boolean;  // Default is false in backend
  is_liked?: boolean;  // Keep this as it's managed client-side
}