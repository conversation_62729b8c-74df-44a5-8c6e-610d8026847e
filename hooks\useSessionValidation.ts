import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export function useSessionValidation() {
  const { data: session, status } = useSession();
  const [isValidating, setIsValidating] = useState(false);
  const router = useRouter();

  const validateSession = async () => {
    if (status !== 'authenticated' || !session?.user?.accessToken) {
      return false;
    }

    setIsValidating(true);
    try {
      // Test the session by making a simple API call
      const response = await fetch('/api/users/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 401) {
        console.log('Session is invalid, redirecting to login...');
        router.push('/auth/login');
        return false;
      }

      return response.ok;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  };

  useEffect(() => {
    if (status === 'authenticated') {
      validateSession();
    }
  }, [status]);

  return {
    isSessionValid: status === 'authenticated' && !!session?.user?.accessToken,
    isValidating,
    validateSession,
  };
}
