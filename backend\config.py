import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Security configuration
SECRET_KEY = os.getenv("NEXTAUTH_SECRET", "your-secret-key-here")  # Use the same secret as NextAuth
ALGORITHM = "HS256"  # JWT encoding algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Token expiration time

# Debug: Print the SECRET_KEY to verify it's loaded correctly
print(f"Backend SECRET_KEY loaded: {SECRET_KEY[:20]}..." if SECRET_KEY else "SECRET_KEY is None or empty")