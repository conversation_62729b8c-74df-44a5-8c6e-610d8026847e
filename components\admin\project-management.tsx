"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Plus, MoreHorizontal, UserPlus, X, Loader } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { projectApi, userApi } from "@/lib/api"
import { Project } from "@/types/project"
import { User } from "@/lib/api"

// Interface for project data with user count
interface ProjectWithUsers extends Project {
  code?: string;
  icon_name?: string;
  description?: string;
}

// Interface for user data with projects
interface UserWithProjects extends User {
  projects: string[];
  role: string;
  status: string;
}

// Mock data for users - will be replaced with API data later
const mockUsers = [
  {
    user_id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    projects: ["OMS", "WMS"],
    created_at: "2023-01-15T10:30:00Z",
    is_admin: true
  },
  {
    user_id: 2,
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "Employee",
    status: "Active",
    projects: ["OMS"],
    created_at: "2023-01-15T10:30:00Z",
    is_admin: false
  },
  {
    user_id: 3,
    name: "Alex Johnson",
    email: "<EMAIL>",
    role: "Employee",
    status: "Active",
    projects: ["WMS", "AUTO"],
    created_at: "2023-01-15T10:30:00Z",
    is_admin: false
  }
]

export function ProjectManagement() {
  const { toast } = useToast()
  const [projects, setProjects] = useState<ProjectWithUsers[]>([])
  const [users, setUsers] = useState<UserWithProjects[]>(mockUsers)
  const [loading, setLoading] = useState<boolean>(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [userSearchQuery, setUserSearchQuery] = useState("")
  const [isAddProjectDialogOpen, setIsAddProjectDialogOpen] = useState(false)
  const [isManageUsersDialogOpen, setIsManageUsersDialogOpen] = useState(false)
  const [isAddUsersDialogOpen, setIsAddUsersDialogOpen] = useState(false)
  const [selectedProject, setSelectedProject] = useState<ProjectWithUsers | null>(null)
  const [selectedUsers, setSelectedUsers] = useState<{ [key: number]: boolean }>({})

  const [newProject, setNewProject] = useState({
    name: "",
    code: "",
    description: "",
  })
  
  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true)
        const projectsData = await projectApi.getProjectsWithUsers()
        
        // Transform the data to match our component's expected format
        const formattedProjects = projectsData.map(project => ({
          ...project,
          code: project.name.split(' ').map(word => word[0]).join('').toUpperCase(), // Generate code from name if not available
          status: "Active" // Status is already set to "Active" by the API
        }))
        
        setProjects(formattedProjects)
      } catch (error) {
        console.error("Error fetching projects:", error)
        toast({
          title: "Error",
          description: "Failed to load projects. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchProjects()
  }, [toast])

  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (project.code && project.code.toLowerCase().includes(searchQuery.toLowerCase())),
  )

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchQuery.toLowerCase()),
  )

  const projectUsers = selectedProject ? users.filter((user) => 
    user.projects.includes(selectedProject.code || "")) : []

  const availableUsers = users.filter((user) => 
    !user.projects.includes(selectedProject?.code || ""))

  const handleAddProject = async () => {
    try {
      // Create project data object
      const projectData = {
        project_name: newProject.name,
        description: newProject.description || "No description provided",
        icon_name: newProject.code || newProject.name.split(' ').map(word => word[0]).join('').toUpperCase(),
      }
      
      // Call API to create project
      const createdProject = await projectApi.createProject(projectData)
      
      // Format the new project to match our component's expected format
      const newProjectWithId: ProjectWithUsers = {
        id: createdProject.project_id,
        name: createdProject.project_name,
        user_count: 0,
        created_at: createdProject.created_at,
        status: "Active",
        code: projectData.icon_name,
        description: projectData.description,
      }
      
      // Add to local state
      setProjects([...projects, newProjectWithId])
      setIsAddProjectDialogOpen(false)
      
      // Reset form
      setNewProject({
        name: "",
        code: "",
        description: "",
      })
      
      toast({
        title: "Project created",
        description: "The project has been created successfully",
      })
    } catch (error) {
      console.error("Error creating project:", error)
      toast({
        title: "Error",
        description: "Failed to create project. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleChangeStatus = async (projectId: number, newStatus: string) => {
    try {
      // In a real implementation, you would call an API to update the project status
      // For now, we'll just update the local state
      
      // Update local state
      setProjects(projects.map((project) => 
        (project.id === projectId ? { ...project, status: newStatus } : project)
      ))
      
      toast({
        title: "Status updated",
        description: `Project status changed to ${newStatus}`,
      })
    } catch (error) {
      console.error("Error updating project status:", error)
      toast({
        title: "Error",
        description: "Failed to update project status. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleRemoveUserFromProject = async (userId: number) => {
    try {
      // In a real implementation, you would call an API to remove the user from the project
      // For now, we'll just update the local state
      
      // Update users state
      setUsers(
        users.map((user) => {
          if (user.user_id === userId) {
            return {
              ...user,
              projects: user.projects.filter((p) => p !== selectedProject?.code),
            }
          }
          return user
        }),
      )

      // Update project user count
      if (selectedProject) {
        const updatedProjects = projects.map((project) => {
          if (project.id === selectedProject.id) {
            return {
              ...project,
              user_count: Math.max(0, (project.user_count || 0) - 1),
            }
          }
          return project
        })
        setProjects(updatedProjects)
      }

      toast({
        title: "User removed",
        description: "User has been removed from the project",
      })
    } catch (error) {
      console.error("Error removing user from project:", error)
      toast({
        title: "Error",
        description: "Failed to remove user from project. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleAddUsersToProject = async () => {
    try {
      // Get selected user IDs
      const selectedUserIds = Object.entries(selectedUsers)
        .filter(([_, isSelected]) => isSelected)
        .map(([id, _]) => Number.parseInt(id))

      if (selectedUserIds.length === 0) {
        toast({
          title: "No users selected",
          description: "Please select at least one user to add to the project",
          variant: "destructive",
        })
        return
      }

     setUsers(
        users.map((user) => {
          if (selectedUserIds.includes(user.user_id) && 
              selectedProject && 
              !user.projects.includes(selectedProject.code || "")) {
            return {
              ...user,
              projects: [...user.projects, selectedProject.code || ""],
            }
          }
          return user
        }),
      )

      // Update project user count
      if (selectedProject) {
        const updatedProjects = projects.map((project) => {
          if (project.id === selectedProject.id) {
            return {
              ...project,
              user_count: (project.user_count || 0) + selectedUserIds.length,
            }
          }
          return project
        })
        setProjects(updatedProjects)
      }

      setIsAddUsersDialogOpen(false)
      setSelectedUsers({})

      toast({
        title: "Users added",
        description: `${selectedUserIds.length} user(s) have been added to the project`,
      })
    } catch (error) {
      console.error("Error adding users to project:", error)
      toast({
        title: "Error",
        description: "Failed to add users to project. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Dialog open={isAddProjectDialogOpen} onOpenChange={setIsAddProjectDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Project
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Project</DialogTitle>
              <DialogDescription>Add a new project to the system.</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Project Name</Label>
                <Input
                  id="name"
                  value={newProject.name}
                  onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Project Code</Label>
                <Input
                  id="code"
                  value={newProject.code}
                  onChange={(e) => setNewProject({ ...newProject, code: e.target.value })}
                />
                <p className="text-xs text-muted-foreground">
                  A short code used to identify the project
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the project..."
                  value={newProject.description}
                  onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddProjectDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddProject}>Add Project</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>


      {/* Full Project Management Table (Advanced View) */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[80px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <Loader/>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredProjects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                  No projects found matching your search.
                </TableCell>
              </TableRow>
            ) : (
              filteredProjects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div>{project.name}</div>
                      <div className="text-xs text-muted-foreground">{project.description}</div>
                    </div>
                  </TableCell>
                  <TableCell>{project.code}</TableCell>
                  <TableCell>{formatDate(project.created_at)}</TableCell>
                  <TableCell>
                    <Badge
                      variant={project.status === "Active" ? "default" : "secondary"}
                      className={project.status === "Active" ? "bg-green-500 hover:bg-green-600" : ""}
                    >
                      {project.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedProject(project)
                            setIsManageUsersDialogOpen(true)
                          }}
                        >
                          Manage Users
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            handleChangeStatus(project.id, project.status === "Active" ? "Inactive" : "Active")
                          }
                        >
                          {project.status === "Active" ? "Deactivate" : "Activate"}
                        </DropdownMenuItem>
                        <DropdownMenuItem>Edit Project</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">Delete Project</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Manage Users Dialog */}
      <Dialog
        open={isManageUsersDialogOpen}
        onOpenChange={(open) => {
          setIsManageUsersDialogOpen(open)
          if (!open) setSelectedProject(null)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Manage Project Users</DialogTitle>
            <DialogDescription>
              {selectedProject && `Manage users for ${selectedProject.name} (${selectedProject.code})`}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="relative w-64">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  className="pl-8"
                  value={userSearchQuery}
                  onChange={(e) => setUserSearchQuery(e.target.value)}
                />
              </div>
              <Button
                size="sm"
                onClick={() => {
                  setIsAddUsersDialogOpen(true)
                  setSelectedUsers({})
                }}
              >
                <UserPlus className="mr-2 h-4 w-4" /> Add Users
              </Button>
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {projectUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                        No users assigned to this project yet.
                      </TableCell>
                    </TableRow>
                  ) : (
                    projectUsers.map((user) => (
                      <TableRow key={user.user_id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant={user.role === "Admin" ? "default" : "outline"}>{user.role}</Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="icon" onClick={() => handleRemoveUserFromProject(user.user_id)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsManageUsersDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Users Dialog */}
      <Dialog
        open={isAddUsersDialogOpen && !!selectedProject}
        onOpenChange={(open) => {
          setIsAddUsersDialogOpen(open)
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add Users to Project</DialogTitle>
            <DialogDescription>
              {selectedProject && `Select users to add to ${selectedProject.name} (${selectedProject.code})`}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="relative w-full mb-4">
              <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-8"
                value={userSearchQuery}
                onChange={(e) => setUserSearchQuery(e.target.value)}
              />
            </div>
            <div className="rounded-md border max-h-[300px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]"></TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {availableUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                        No available users to add.
                      </TableCell>
                    </TableRow>
                  ) : (
                    availableUsers
                      .filter(
                        (user) =>
                          user.name.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
                          user.email.toLowerCase().includes(userSearchQuery.toLowerCase()),
                      )
                      .map((user) => (
                        <TableRow key={user.user_id}>
                          <TableCell>
                            <Checkbox
                              checked={!!selectedUsers[user.user_id]}
                              onCheckedChange={(checked) => {
                                setSelectedUsers({
                                  ...selectedUsers,
                                  [user.user_id]: !!checked,
                                })
                              }}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{user.name}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge variant={user.role === "Admin" ? "default" : "outline"}>{user.role}</Badge>
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddUsersDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddUsersToProject}>Add Selected Users</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

