import { getSession } from 'next-auth/react';

export const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || 'http://localhost:8000';

// Custom error class for API errors
export class ApiError extends Error {
  status: number;
  isAuthError: boolean;

  constructor(message: string, status: number) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.isAuthError = status === 401;
  }
}

// Define TypeScript interfaces based on the database models


export interface User {
  user_id: number;
  name: string;
  email: string;
  created_at: string;
  is_admin: boolean;
  role: string;
}

export interface Project {
  project_id: number;
  project_name: string;
  description: string;
  icon_name: string;
  created_at: string;
}

export enum CategoryEnum {
  Exceptions = 'EXCEPTIONS',
  CommonIssues = 'COMMON_ISSUES',
  Misc = 'MISC',
  IgnoredExceptions = 'IGNORED_EXCEPTIONS'
}

export enum StatusEnum {
  Open = 'Open',
  InProgress = 'In Progress',
  Resolved = 'Resolved',
  Closed = 'Closed'
}

export enum ImpactEnum {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Critical = 'Critical'
}

export enum FrequencyEnum {
  Rare = 'Rare',
  Occasional = 'Occasional',
  Frequent = 'Frequent',
  Always = 'Always'
}

export enum SystemNameEnum {
  OMS = 'OMS',
  WMS = 'WMS',
  AUTOMATION = 'AUTOMATION',
  OTHERS = 'OTHERS'
}

export interface Issue {
  issue_id: number;
  project_id: number;
  category: CategoryEnum;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name?: SystemNameEnum;
  issue_type?: string;
  status: StatusEnum;
  impact?: ImpactEnum;
  frequency?: FrequencyEnum;
  description: string;
  jira_id?: string | number | null;
  jira_link?: string | number | null;
  hemants_view?: string;
  created_by: number;
  created_at: string;
  reviewed_by?: number;
  reviewed_at?: string;
}

export interface Solution {
  solution_id: number;
  issue_id: number;
  category: CategoryEnum;
  provided_by: number;
  provided_by_name?: string;  // Add the provider's name
  solution_text: string;
  upvotes: number;
  created_at: string;
}

export interface KnowledgeBase {
  kb_id: number;
  project_id: number;
  title: string;
  content: string;
  solution_id?: number;
  solution?: string;
  uploaded_by: number;
  file_url?: string;
  created_at: string;
  author_name?: string;
  verified?: boolean;
  views?: number;
  likes?: number;
  system?: string;
  type?: string;
  is_favorite?: boolean;
  is_liked?: boolean;
}

export interface IssueCreateData {
  project_id: number;
  category: CategoryEnum;
  title: string;
  description: string;
  error_code?: string;
  error_message?: string;
  system_name?: SystemNameEnum;
  issue_type?: string;
  status?: StatusEnum;
  impact?: ImpactEnum;
  frequency?: FrequencyEnum;
  jira_id?: string | number | null;
  jira_link?: string | number | null;
  hemants_view?: string;
}

export interface SolutionCreateData {
  issue_id: number;
  category: CategoryEnum;
  solution_text: string;
  provided_by?: number;  // Make this optional as it will be set by the backend
}

export interface KnowledgeBaseCreateData {
  project_id: number;
  title: string;
  content: string;
  solution?: string;
  file_url?: string;
}

export interface SearchFilters {
  category?: CategoryEnum;
  status?: StatusEnum;
  impact?: ImpactEnum;
  frequency?: FrequencyEnum;
  date_range?: [string, string];
  min_upvotes?: number;
}
/**
 * Refreshes the authentication token using the refresh token endpoint
 * @returns The new access token or null if refresh failed
 */
async function refreshToken(): Promise<string | null> {
  try {
    const refreshUrl = `${API_BASE_URL}/refresh-token`;

    const response = await fetch(refreshUrl, {
      method: 'POST',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to refresh token');
    }

    const data = await response.json();

    // If we got a new token, update the session
    if (data.access_token) {
      // This is a client-side update that will be used until the session is refreshed
      const session = await getSession();
      if (session?.user) {
        session.user.accessToken = data.access_token;
      }
    }

    return data.access_token;
  } catch (error) {
    console.error('Token refresh failed:', error);
    return null;
  }
}

/**
 * Base API request function with authentication, token refresh, and error handling
 * @param endpoint API endpoint path
 * @param options Fetch options
 * @returns Promise with the response data
 */
export async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  // Initialize token variable outside the try block
  let token: string | undefined;

  try {
    const session = await getSession();

    if (!session?.user?.id) {
      throw new ApiError('User not authenticated', 401);
    }

    // Get the token from the session
    token = session.user.accessToken;
    if (!token) {
      throw new ApiError('No access token available', 401);
    }
  } catch (error) {
    // If we're in development mode or using mock data, allow the request to proceed
    // without authentication for certain endpoints
    const isDev = process.env.NODE_ENV === 'development';
    const useMockData = isDev && (process.env['NEXT_PUBLIC_USE_MOCK_DATA'] === 'true' || !process.env['NEXT_PUBLIC_API_URL']);

    if (useMockData && (endpoint.includes('/favorites') || endpoint.includes('/issues'))) {
      console.warn('Using mock data mode for endpoint:', endpoint);

      // For favorites endpoints, return mock responses
      if (endpoint === '/favorites/' && options.method === 'GET') {
        console.log("Using mock data for GET /favorites/");
        return { items: [], total: 0 } as unknown as T;
      } else if (endpoint === '/favorites/' && options.method === 'POST') {
        // Extract the issue_id from the request body
        let issueId = 0;
        try {
          if (options.body && typeof options.body === 'string') {
            const body = JSON.parse(options.body);
            issueId = body.issue_id;
          }
        } catch (e) {
          console.error("Error parsing POST body:", e);
        }
        console.log("Using mock data for POST /favorites/ with issue_id:", issueId);
        return {
          favorite_id: Math.floor(Math.random() * 1000),
          user_id: 1,
          issue_id: issueId,
          created_at: new Date().toISOString()
        } as unknown as T;
      } else if (endpoint.includes('/favorites/') && options.method === 'DELETE') {
        console.log("Using mock data for DELETE /favorites/");
        return { success: true } as unknown as T;
      }
    }

    // For other cases, rethrow the error
    throw error;
  }

  // Check if this is an auth-related endpoint
  const isTokenEndpoint = endpoint.includes('/token');
  const isRefreshEndpoint = endpoint.includes('/refresh-token');

  // Set appropriate content type
  const contentType = isTokenEndpoint ? 'application/x-www-form-urlencoded' : 'application/json';

  // Add authorization header using Bearer token
  const headers: HeadersInit = {
    'Content-Type': contentType,
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    ...(options.headers as Record<string, string> || {}),
  };

  // Handle form data for token endpoint
  let modifiedBody = options.body;
  if (isTokenEndpoint && options.body && typeof options.body === 'string') {
    try {
      const jsonBody = JSON.parse(options.body);
      const formData = new URLSearchParams();
      Object.entries(jsonBody).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
      modifiedBody = formData;
    } catch (e) {
      console.error('Error parsing JSON body:', e);
    }
  }

  // Create request options
  const requestOptions: RequestInit = {
    ...options,
    headers,
    credentials: 'include', // Include cookies for cross-origin requests
  };

  // Only add body if it's not undefined
  if (modifiedBody !== undefined) {
    requestOptions.body = modifiedBody;
  }

  // Make the initial request
  let response;
  try {
    response = await fetch(`${API_BASE_URL}${endpoint}`, requestOptions);
  } catch (error) {
    // Handle network errors (e.g., server unreachable, CORS issues)
    console.error('Network error during API request:', error);
    throw new ApiError(`Network error: ${error instanceof Error ? error.message : 'Failed to fetch'}`, 0);
  }

  // Handle 401 Unauthorized errors by attempting to refresh the token
  if (response.status === 401 && !isTokenEndpoint && !isRefreshEndpoint) {
    const newToken = await refreshToken();
    if (newToken) {
      // Update the authorization header with the new token
      headers['Authorization'] = `Bearer ${newToken}`;

      // Create a new request options object for the retry
      const retryRequestOptions: RequestInit = {
        ...options,
        headers,
        credentials: 'include',
      };

      // Only add body if it's not undefined
      if (modifiedBody !== undefined) {
        retryRequestOptions.body = modifiedBody;
      }

      // Retry the request with the new token
      try {
        response = await fetch(`${API_BASE_URL}${endpoint}`, retryRequestOptions);
      } catch (error) {
        console.error('Network error during token refresh retry:', error);
        throw new ApiError(`Network error after token refresh: ${error instanceof Error ? error.message : 'Failed to fetch'}`, 0);
      }
    } else {
      // If running in browser, redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = `/auth/login?returnUrl=${encodeURIComponent(window.location.pathname + window.location.search)}`;
      }
      throw new ApiError('Authentication failed and token refresh was unsuccessful', 401);
    }
  }

  // Handle error responses
  if (!response.ok) {
    let errorMessage = 'API request failed';
    let errorData: any = {};

    try {
      errorData = await response.json();
      errorMessage = errorData.detail || errorData.error || errorMessage;
    } catch (e) {
      // If parsing JSON fails, use the status text
      errorMessage = response.statusText || errorMessage;
    }

    // Create a more descriptive error message
    const statusText = response.statusText ? ` (${response.statusText})` : '';
    const fullErrorMessage = `${errorMessage} - Status: ${response.status}${statusText}`;

    throw new ApiError(fullErrorMessage, response.status);
  }

  // Check if response is JSON
  try {
    const contentTypeHeader = response.headers.get('content-type');
    if (contentTypeHeader && contentTypeHeader.includes('application/json')) {
      return await response.json();
    }
  } catch (error) {
    console.error('Error parsing JSON response:', error);
    throw new ApiError(`Failed to parse JSON response: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
  }

  // For non-JSON responses
  return { success: true } as any;
}

/**
 * Generic response interface for collections
 */
export interface ApiResponse<T> {
  items: T[];
  total: number;
}


// User API functions
export const userApi = {
  /**
   * Get current user profile
   */
  getCurrentUser(): Promise<User> {
    // Use the special 'me' path parameter to get the current user
    return apiRequest<User>('/users/me');
  },

  /**


  /**
   * Get user by ID
   */
  getUser(userId: number): Promise<User> {
    return apiRequest<User>(`/users/${userId}`);
  },

  /**
   * Update user profile
   */
  updateUser(userId: number, userData: Partial<User>): Promise<User> {
    return apiRequest<User>(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }
};

// Project API functions
export const projectApi = {
  /**
   * Get all projects with user counts
   */
  getProjectsWithUsers(): Promise<import('@/types/project').Project[]> {
    return apiRequest<import('@/types/project').Project[]>('/projects/with-users/');
  },

  /**
   * Get project by ID
   */
  getProject(projectId: number): Promise<import('@/types/project').Project> {
    return apiRequest<import('@/types/project').Project>(`/projects/${projectId}`);
  },

  /**
   * Create a new project
   */
  createProject(data: import('@/types/project').ProjectCreate): Promise<any> {
    return apiRequest<any>('/projects/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  /**
   * Update project
   */
  updateProject(projectId: number, data: Partial<Project>): Promise<Project> {
    return apiRequest<Project>(`/projects/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  /**
   * Delete project
   */
  deleteProject(projectId: number): Promise<{ success: boolean }> {
    return apiRequest<{ success: boolean }>(`/projects/${projectId}`, {
      method: 'DELETE',
    });
  },
  
  /**
   * Get users for a project
   */
  getProjectUsers(projectId: number): Promise<User[]> {
    return apiRequest<User[]>(`/projects/${projectId}/users`);
  },
  
  /**
   * Add user to project
   */
  addUserToProject(projectId: number, userId: number, role: string = 'USER'): Promise<any> {
    return apiRequest<any>(`/projects/${projectId}/users`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId, role }),
    });
  },
  
  /**
   * Remove user from project
   */
  removeUserFromProject(projectId: number, userId: number): Promise<{ success: boolean }> {
    return apiRequest<{ success: boolean }>(`/projects/${projectId}/users/${userId}`, {
      method: 'DELETE',
    });
  }
}

// Issue API functions
export const issueApi = {

  /**
   * Get issues with search and filters
   */
  searchIssues(projectId: number, query: string = '', filters?: SearchFilters): Promise<ApiResponse<Issue>> {
    const searchParams = new URLSearchParams();
    if (query) searchParams.append('query', query);
    if (filters?.category) searchParams.append('category', filters.category);
    if (filters?.status) searchParams.append('status', filters.status);
    if (filters?.impact) searchParams.append('impact', filters.impact);
    if (filters?.frequency) searchParams.append('frequency', filters.frequency);
    if (filters?.date_range) {
      searchParams.append('date_from', filters.date_range[0]);
      searchParams.append('date_to', filters.date_range[1]);
    }
    if (filters?.min_upvotes) searchParams.append('min_upvotes', filters.min_upvotes.toString());

    // Construct URL without trailing question mark if no parameters
    const queryString = searchParams.toString();
    const endpoint = queryString
      ? `/projects/${projectId}/issues?${queryString}`
      : `/projects/${projectId}/issues`;

    return apiRequest<ApiResponse<Issue>>(endpoint);
  },

  /**
   * Get issue by ID
   */
  getIssue(issueId: number): Promise<Issue> {
    return apiRequest<Issue>(`/issues/${issueId}`);
  },

  /**
   * Create a new issue
   */
  createIssue(data: IssueCreateData): Promise<Issue> {
    return apiRequest<Issue>('/issues/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  /**
   * Update issue status
   */
  updateIssueStatus(issueId: number, status: StatusEnum): Promise<Issue> {
    return apiRequest<Issue>(`/issues/${issueId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },

  /**
   * Update issue details
   */
  updateIssue(issueId: number, data: Partial<IssueCreateData>): Promise<Issue> {
    return apiRequest<Issue>(`/issues/${issueId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  /**
   * Delete an issue
   */
  deleteIssue(issueId: number): Promise<{ success: boolean }> {
    return apiRequest<{ success: boolean }>(`/issues/${issueId}`, {
      method: 'DELETE',
    });
  }
};

// Solution API functions
export const solutionApi = {
  /**
   * Get all solutions
   */
  getSolutions(): Promise<ApiResponse<Solution>> {
    return apiRequest<ApiResponse<Solution>>('/solutions');
  },

  /**
   * Get solutions for an issue
   * @param issueId The issue ID
   */
  getIssueSolutions(issueId: number): Promise<ApiResponse<Solution>> {
    // Validate issueId
    if (!issueId || isNaN(Number(issueId))) {
      return Promise.reject(new ApiError(`Invalid issue ID: ${issueId}`, 400));
    }

    console.log(`API Request: GET /issues/${issueId}/solutions`);
    return apiRequest<ApiResponse<Solution>>(`/issues/${issueId}/solutions`);
  },

  /**
   * Get solution by ID
   */
  getSolution(solutionId: number): Promise<Solution> {
    return apiRequest<Solution>(`/solutions/${solutionId}`);
  },

  /**
   * Create a new solution
   */
  createSolution(data: SolutionCreateData): Promise<Solution> {
    return apiRequest<Solution>('/solutions', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  /**
   * Update a solution
   */
  updateSolution(solutionId: number, data: Partial<SolutionCreateData>): Promise<Solution> {
    return apiRequest<Solution>(`/solutions/${solutionId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  /**
   * Upvote a solution
   */
  upvoteSolution(solutionId: number): Promise<Solution> {
    return apiRequest<Solution>(`/solutions/${solutionId}/upvote`, {
      method: 'POST',
    });
  },

  /**
   * Delete a solution
   */
  deleteSolution(solutionId: number): Promise<{ success: boolean }> {
    return apiRequest<{ success: boolean }>(`/solutions/${solutionId}`, {
      method: 'DELETE',
    });
  }
};

// Define the favorites API client
export interface Favorite {
  favorite_id?: number;
  user_id?: number;
  issue_id: number;
  created_at?: string;
}

export const favoritesApi = {
  /**
   * Get all favorites for the current user
   */
  getUserFavorites(): Promise<ApiResponse<Favorite>> {
    return apiRequest<ApiResponse<Favorite>>('/favorites/')
      .then(response => {
        // Ensure the response has the expected structure
        if (!response.items) {
          console.warn('Unexpected response format from favorites API:', response);
          // Convert to expected format if needed
          if (Array.isArray(response)) {
            return { items: response, total: response.length };
          }
          // Return empty array as fallback
          return { items: [], total: 0 };
        }
        return response;
      })
      .catch(error => {
        console.error('Error fetching favorites:', error);
        // Return empty array on error
        return { items: [], total: 0 };
      });
  },

  /**
   * Add an issue to favorites
   */
  addFavorite(issueId: number): Promise<Favorite> {
    console.log(`Adding issue ${issueId} to favorites`);
    return apiRequest<Favorite>('/favorites/', {
      method: 'POST',
      body: JSON.stringify({ issue_id: issueId }),
    })
    .then(response => {
      console.log(`Successfully added issue ${issueId} to favorites:`, response);
      return response;
    })
    .catch(error => {
      console.error(`Error adding issue ${issueId} to favorites:`, error);
      // Rethrow to allow the component to handle the error
      throw error;
    });
  },

  /**
   * Remove an issue from favorites
   */
  removeFavorite(issueId: number): Promise<void> {
    console.log(`Removing issue ${issueId} from favorites`);
    return apiRequest<void>(`/favorites/${issueId}`, {
      method: 'DELETE',
    })
    .then(response => {
      console.log(`Successfully removed issue ${issueId} from favorites`);
      return response;
    })
    .catch(error => {
      console.error(`Error removing issue ${issueId} from favorites:`, error);

      // If the error is a 404 (not found), we can consider this a success
      // since the end result is the same - the favorite doesn't exist
      if (error instanceof ApiError && error.status === 404) {
        console.log(`Issue ${issueId} was not in favorites, treating as success`);
        return; // Return successfully
      }

      // Rethrow other errors
      throw error;
    });
  }
};


