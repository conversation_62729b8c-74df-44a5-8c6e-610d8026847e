# Extrack Project AI Assistant Instructions

## Project Overview
Extrack is a full-stack exception tracking and knowledge management system with Next.js frontend and FastAPI backend. The system helps teams track software issues and maintain a knowledge base.

## Architecture Patterns

### Frontend (Next.js + TypeScript)
- Page components in `app/` follow Next.js 13+ App Router conventions
- Shared UI components in `components/` use Radix UI primitives
- Global state managed through React Context (`contexts/`)
- Custom hooks in `hooks/` for reusable logic
- Type definitions in `types/` for shared interfaces

### Backend (FastAPI + SQLAlchemy)
- Routes modularized in `backend/routers/`
- SQLAlchemy models in `backend/models.py`
- Pydantic schemas in `backend/schemas.py`
- Business logic in `backend/crud.py`
- Dependency injection through `backend/dependencies.py`

## Key Development Workflows

### Starting the Development Environment
```bash
# Frontend development server
npm run dev:frontend  # Runs on http://localhost:3000

# Backend development server
npm run dev:backend   # Runs on http://localhost:8000
```

### Database Operations
- Schema defined in `backend/DB/structure_v1.sql`
- Migrations managed with Alembic
- Use SQLAlchemy for all database operations
- Example query pattern:
```python
db.query(models.Project).options(joinedload(models.Project.users)).filter(...).all()
```

### Authentication Flow
- JWT-based auth implemented in `backend/auth.py`
- Protected routes require `get_current_user` dependency
- Admin routes use `require_admin` dependency
- Project access verified through `verify_project_access`

## Project-Specific Conventions

### API Patterns
- Routes follow `/api/{entity}/` structure
- Standard CRUD operations use consistent patterns:
  - List: GET `/api/{entity}/`
  - Detail: GET `/api/{entity}/{id}`
  - Create: POST `/api/{entity}/`
  - Update: PATCH `/api/{entity}/{id}`
  - Delete: DELETE `/api/{entity}/{id}`

### Error Handling
- Frontend: Use custom hooks for API error handling
- Backend: Raise HTTPException with appropriate status codes
- Always include error context in responses

### Type Safety
- Use Zod for runtime validation
- Keep Pydantic models and TypeScript interfaces in sync
- Generate TypeScript types from OpenAPI specs

## Integration Points
- Frontend-Backend: REST API with OpenAPI/Swagger docs
- Authentication: JWT tokens via `/api/auth/` endpoints
- Real-time Updates: Server-sent events for live issue tracking
- External Services: Environment variables in `.env` files
