import { cn } from "@/lib/utils"
import { StatusInfo } from "@/types/issue-details"

type StatusBadgeProps = {
  status: string | StatusEnum
  statusInfo: StatusInfo
  className?: string
}

// Import StatusEnum from the API types
import { StatusEnum } from "@/lib/api"

export const StatusBadge = ({ status, statusInfo, className }: StatusBadgeProps) => {
  return (
    <span
      className={cn(
        "px-2.5 py-1 flex items-center gap-1.5 font-medium ring-1 shadow-sm text-sm rounded-md transition-colors duration-200 ease-in-out",
        {
          'bg-rose-100 text-rose-700 ring-rose-200 hover:bg-rose-200 dark:bg-rose-900/60 dark:text-rose-100 dark:ring-rose-800 dark:hover:bg-rose-900/80': status === StatusEnum.Open,
          'bg-amber-100 text-amber-700 ring-amber-200 hover:bg-amber-200 dark:bg-amber-900/60 dark:text-amber-100 dark:ring-amber-800 dark:hover:bg-amber-900/80': status === StatusEnum.InProgress,
          'bg-blue-100 text-blue-700 ring-blue-200 hover:bg-blue-200 dark:bg-blue-900/60 dark:text-blue-100 dark:ring-blue-800 dark:hover:bg-blue-900/80': status === StatusEnum.Resolved,
          'bg-emerald-100 text-emerald-700 ring-emerald-200 hover:bg-emerald-200 dark:bg-emerald-900/60 dark:text-emerald-100 dark:ring-emerald-800 dark:hover:bg-emerald-900/80': status === StatusEnum.Closed,
          'bg-slate-100 text-slate-700 ring-slate-200 hover:bg-slate-200 dark:bg-slate-800/60 dark:text-slate-100 dark:ring-slate-700 dark:hover:bg-slate-800/80': !Object.values(StatusEnum).includes(status as StatusEnum),
        },
        className
      )}
    >
      {statusInfo.icon}
      {status}
    </span>
  )
}
