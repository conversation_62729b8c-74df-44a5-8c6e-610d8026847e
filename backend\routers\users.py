from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from typing import List
from ..database import get_db
from .. import models
from ..schemas import UserUpdate, UserResponse
from ..dependencies import get_current_user
from sqlalchemy.orm import load_only

router = APIRouter()

@router.get("/users", response_model=List[UserResponse])
async def get_users(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    """Get all users"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Eager load the admin relationship to avoid N+1 queries
    users = db.query(models.User).options(joinedload(models.User.admin)).all()
    
    # Force the is_admin property to be evaluated while the session is active
    for user in users:
        user.is_admin  # This will trigger the hybrid property evaluation
    
    return users

@router.patch("/users/{user_id}/role")
async def update_user_role(
    user_id: int, 
    role_data: dict, 
    db: Session = Depends(get_db), 
    current_user: models.User = Depends(get_current_user)
):
    """Update a user's role (admin only)"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized to access this resource")
    
    # Validate role
    if "role" not in role_data or role_data["role"] not in ["Admin", "User"]:
        raise HTTPException(status_code=400, detail="Invalid role. Must be 'Admin' or 'User'")
    
    # Get the user
    user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    try:
        is_admin = (role_data["role"].lower() == "admin")
        
        # Handle admin table relationship
        if is_admin and not user.admin:
            # Create admin record if user should be admin and isn't
            new_admin = models.Admin(user_id=user.user_id)
            db.add(new_admin)
        elif not is_admin and user.admin:
            # Remove admin record if user should not be admin
            db.query(models.Admin).filter(models.Admin.user_id == user.user_id).delete()
        
        db.commit()
        
        # If demoting from admin, remove from admin table
        if not user.is_admin and user.admin:
            db.delete(user.admin)
        # If promoting to admin, add to admin table if not already there
        elif user.is_admin and not user.admin:
            admin = models.Admin(user_id=user.user_id)
            db.add(admin)
        
        db.commit()
        return {"message": f"User role updated to {role_data['role']} successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Update a user's information"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")

    db_user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update user fields if provided
    if user_update.name is not None:
        db_user.name = user_update.name
    if user_update.email is not None:
        db_user.email = user_update.email
    if user_update.role is not None:
        db_user.role = user_update.role
        db_user.is_admin = (user_update.role.lower() == "admin")
    if user_update.status is not None:
        db_user.status = user_update.status

    # --- Project assignment logic ---
    if user_update.projects is not None:
        # Remove all existing project assignments for this user
        db.query(models.UserProjectRole).filter(models.UserProjectRole.user_id == user_id).delete()
        # Add new assignments
        for project_code in user_update.projects:
            # Find project by code or name
            project = db.query(models.Project).filter((models.Project.project_name == project_code) | (models.Project.icon_name == project_code)).first()
            if project:
                new_role = models.UserProjectRole(
                    user_id=user_id,
                    project_id=project.project_id,
                    role="ADMIN" if db_user.is_admin else "USER"
                )
                db.add(new_role)
    # --- End project assignment logic ---

    try:
        db.commit()
        db.refresh(db_user)
        return db_user
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """Delete a user"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Not authorized")

    db_user = db.query(models.User).filter(models.User.user_id == user_id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    try:
        db.delete(db_user)
        db.commit()
        return None
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))